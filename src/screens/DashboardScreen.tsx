import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';

export default function DashboardScreen() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>儀表板</Text>
        <Text style={styles.subtitle}>財務概覽</Text>
      </View>
      
      <View style={styles.card}>
        <Text style={styles.cardTitle}>總資產</Text>
        <Text style={styles.amount}>$0</Text>
      </View>
      
      <View style={styles.card}>
        <Text style={styles.cardTitle}>本月收支</Text>
        <Text style={styles.amount}>$0</Text>
      </View>
      
      <View style={styles.card}>
        <Text style={styles.cardTitle}>最近交易</Text>
        <Text style={styles.placeholder}>暫無交易記錄</Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#fff',
    padding: 20,
    marginBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 5,
  },
  card: {
    backgroundColor: '#fff',
    margin: 10,
    padding: 20,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 10,
  },
  amount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  placeholder: {
    fontSize: 14,
    color: '#999',
    fontStyle: 'italic',
  },
});
