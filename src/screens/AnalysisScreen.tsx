import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';

export default function AnalysisScreen() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>收支分析</Text>
        <Text style={styles.subtitle}>分析您的收入與支出</Text>
      </View>
      
      <View style={styles.card}>
        <Text style={styles.cardTitle}>本月收支</Text>
        <View style={styles.row}>
          <View style={styles.column}>
            <Text style={styles.label}>收入</Text>
            <Text style={[styles.amount, styles.income]}>$0</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.label}>支出</Text>
            <Text style={[styles.amount, styles.expense]}>$0</Text>
          </View>
          <View style={styles.column}>
            <Text style={styles.label}>結餘</Text>
            <Text style={styles.amount}>$0</Text>
          </View>
        </View>
      </View>
      
      <View style={styles.card}>
        <Text style={styles.cardTitle}>支出分類</Text>
        <Text style={styles.placeholder}>暫無支出數據</Text>
      </View>
      
      <View style={styles.card}>
        <Text style={styles.cardTitle}>收入來源</Text>
        <Text style={styles.placeholder}>暫無收入數據</Text>
      </View>
      
      <View style={styles.card}>
        <Text style={styles.cardTitle}>趨勢分析</Text>
        <Text style={styles.placeholder}>暫無趨勢數據</Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#fff',
    padding: 20,
    marginBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 5,
  },
  card: {
    backgroundColor: '#fff',
    margin: 10,
    padding: 20,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 15,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  column: {
    alignItems: 'center',
  },
  label: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  amount: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  income: {
    color: '#4CAF50',
  },
  expense: {
    color: '#F44336',
  },
  placeholder: {
    fontSize: 14,
    color: '#999',
    fontStyle: 'italic',
    textAlign: 'center',
  },
});
