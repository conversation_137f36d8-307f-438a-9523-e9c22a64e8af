import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';

export default function ChartsScreen() {
  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>圖表分析</Text>
        <Text style={styles.subtitle}>視覺化財務數據</Text>
      </View>
      
      <View style={styles.card}>
        <Text style={styles.cardTitle}>收支趨勢圖</Text>
        <View style={styles.chartPlaceholder}>
          <Text style={styles.placeholder}>圖表功能即將推出</Text>
        </View>
      </View>
      
      <View style={styles.card}>
        <Text style={styles.cardTitle}>支出分類圓餅圖</Text>
        <View style={styles.chartPlaceholder}>
          <Text style={styles.placeholder}>圖表功能即將推出</Text>
        </View>
      </View>
      
      <View style={styles.card}>
        <Text style={styles.cardTitle}>資產配置圖</Text>
        <View style={styles.chartPlaceholder}>
          <Text style={styles.placeholder}>圖表功能即將推出</Text>
        </View>
      </View>
      
      <View style={styles.card}>
        <Text style={styles.cardTitle}>月度對比圖</Text>
        <View style={styles.chartPlaceholder}>
          <Text style={styles.placeholder}>圖表功能即將推出</Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#fff',
    padding: 20,
    marginBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 5,
  },
  card: {
    backgroundColor: '#fff',
    margin: 10,
    padding: 20,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 15,
  },
  chartPlaceholder: {
    height: 150,
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#e0e0e0',
    borderStyle: 'dashed',
  },
  placeholder: {
    fontSize: 14,
    color: '#999',
    fontStyle: 'italic',
  },
});
