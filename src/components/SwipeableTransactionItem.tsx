import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
// 暫時移除 Swipeable 以修復 iOS 閃退問題
// import { Swipeable } from 'react-native-gesture-handler';
import { RecurringFrequency } from '../types';

// 1.5個垃圾桶寬度
const DELETE_BUTTON_WIDTH = 120;

interface SwipeableTransactionItemProps {
  item: any;
  account: any;
  category: any;
  isFutureTransaction: boolean;
  onDelete: (item: any, deleteType?: 'single' | 'future' | 'all') => void;
  onEdit?: (item: any) => void;
  formatCurrency: (amount: number) => string;
}

export default function SwipeableTransactionItem({
  item,
  account,
  category,
  isFutureTransaction,
  onDelete,
  onEdit,
  formatCurrency,
}: SwipeableTransactionItemProps) {

  const handleDelete = () => {
    console.log('🗑️ 可靠刪除：交易刪除被觸發，交易ID:', item.id);

    if (!onDelete) {
      console.error('❌ 可靠刪除：onDelete回調函數未定義');
      Alert.alert('錯誤', '刪除功能暫時不可用');
      return;
    }

    // 🔧 WEB 環境測試：直接執行刪除，跳過確認對話框
    console.log('🗑️ 可靠刪除：WEB 環境直接執行交易刪除測試');
    console.log('🗑️ 可靠刪除：用戶確認刪除，調用onDelete');
    try {
      onDelete(item);
      console.log('✅ 可靠刪除：刪除調用成功');
    } catch (error) {
      console.error('❌ 可靠刪除：刪除調用失敗:', error);
    }
  };

  // 🗑️ 可靠刪除：渲染右滑刪除按鈕
  const renderRightActions = () => {
    return (
      <Animated.View style={styles.deleteAction}>
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={handleDelete}
          activeOpacity={0.6}
        >
          <Ionicons name="trash" size={24} color="#fff" />
          <Text style={styles.deleteText}>刪除</Text>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  return (
    <View style={styles.swipeableContainer}>
      <TouchableOpacity
        style={styles.transactionItem}
        onPress={() => onEdit?.(item)}
        activeOpacity={0.7}
      >
        <View style={styles.transactionLeft}>
          <View style={[
            styles.categoryIcon,
            { backgroundColor: category?.color || '#007AFF' }
          ]}>
            <Ionicons
              name={item.type === 'transfer' ? 'swap-horizontal' : (category?.icon as any || 'card-outline')}
              size={20}
              color="#fff"
            />
          </View>
          <View style={styles.transactionInfo}>
            <View style={styles.transactionTitleRow}>
              <Text style={styles.transactionDescription}>
                {/* 修改：主要顯示類別，描述作為小字註記 */}
                {category?.name || '未分類'}
                {item.description && item.description !== (category?.name || '未分類') && (
                  <Text style={styles.descriptionNote}> {item.description}</Text>
                )}
              </Text>
              {item.is_recurring && (
                <View style={styles.recurringBadge}>
                  <Ionicons name="repeat" size={12} color="#007AFF" />
                </View>
              )}
            </View>
            <Text style={styles.transactionAccount}>
              {item.type === 'transfer'
                ? `${item.fromAccount || '未知'} → ${item.toAccount || '未知'}`
                : (account?.name || item.account || '未知帳戶')
              }
              {item.is_recurring && item.recurring_frequency && (
                <Text style={styles.recurringText}>
                  {' • '}
                  {item.recurring_frequency === RecurringFrequency.DAILY && '每日'}
                  {item.recurring_frequency === RecurringFrequency.WEEKLY && '每週'}
                  {item.recurring_frequency === RecurringFrequency.MONTHLY && '每月'}
                  {item.recurring_frequency === RecurringFrequency.YEARLY && '每年'}
                </Text>
              )}
            </Text>
          </View>
        </View>
        <View style={styles.transactionRight}>
          <Text style={[
            styles.transactionAmount,
            item.type === 'transfer' ? styles.transferAmount :
            (item.type === 'income' ? styles.incomeAmount : styles.expenseAmount)
          ]}>
            {item.type === 'transfer' ? '' : (item.type === 'income' ? '+' : '-')}
            {formatCurrency(Math.abs(item.amount))}
          </Text>
          {/* 🔧 WEB 環境臨時刪除按鈕 - 防止事件冒泡 */}
          <TouchableOpacity
            onPress={(e) => {
              e.stopPropagation();
              console.log('🗑️ 刪除按鈕被點擊 - 交易ID:', item.id);
              handleDelete();
            }}
            style={styles.webDeleteButton}
            hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
          >
            <Ionicons name="trash-outline" size={18} color="#FF3B30" />
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
      {/* 暫時添加刪除按鈕以替代滑動功能 */}
      <TouchableOpacity
        style={styles.tempDeleteButton}
        onPress={handleDelete}
        activeOpacity={0.7}
      >
        <Ionicons name="trash-outline" size={20} color="#FF3B30" />
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  // 容器樣式
  swipeableContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  // 臨時刪除按鈕樣式
  tempDeleteButton: {
    padding: 12,
    marginLeft: 8,
    borderRadius: 8,
    backgroundColor: '#FFF2F2',
    justifyContent: 'center',
    alignItems: 'center',
  },
  // 刪除按鈕樣式（1.5個垃圾桶寬度）
  deleteAction: {
    backgroundColor: '#FF3B30',
    justifyContent: 'center',
    alignItems: 'center',
    width: DELETE_BUTTON_WIDTH,
  },
  deleteButton: {
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: '100%',
  },
  deleteText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    marginTop: 4,
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#fff',
    marginHorizontal: 16,
    marginVertical: 4,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  transactionDescription: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    flex: 1,
  },
  descriptionNote: {
    fontSize: 12,
    fontWeight: '400',
    color: '#666',
  },
  recurringBadge: {
    backgroundColor: '#E3F2FD',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  transactionAccount: {
    fontSize: 14,
    color: '#666',
  },
  recurringText: {
    fontSize: 12,
    color: '#007AFF',
    fontWeight: '500',
  },
  transactionRight: {
    alignItems: 'flex-end',
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  incomeAmount: {
    color: '#34C759',
  },
  expenseAmount: {
    color: '#FF3B30',
  },
  transferAmount: {
    color: '#007AFF',
  },
  transactionTime: {
    fontSize: 12,
    color: '#999',
  },
  webDeleteButton: {
    padding: 10,
    borderRadius: 6,
    backgroundColor: 'rgba(255, 59, 48, 0.15)',
    marginLeft: 10,
    minWidth: 40,
    minHeight: 40,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 999,
  },

});
