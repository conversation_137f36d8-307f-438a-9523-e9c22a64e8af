apiVersion: apps/v1
kind: Deployment
metadata:
  name: fintranzo-web
  namespace: fintranzo
  labels:
    app: fintranzo-web
    tier: frontend
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: fintranzo-web
  template:
    metadata:
      labels:
        app: fintranzo-web
        tier: frontend
    spec:
      containers:
      - name: fintranzo-web
        image: fintranzo/web:latest
        ports:
        - containerPort: 80
          name: http
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: fintranzo-config
              key: NODE_ENV
        - name: EXPO_PUBLIC_SUPABASE_URL
          valueFrom:
            secretKeyRef:
              name: fintranzo-secrets
              key: EXPO_PUBLIC_SUPABASE_URL
        - name: EXPO_PUBLIC_SUPABASE_ANON_KEY
          valueFrom:
            secretKeyRef:
              name: fintranzo-secrets
              key: EXPO_PUBLIC_SUPABASE_ANON_KEY
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          runAsNonRoot: true
          runAsUser: 1001
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: nginx-cache
          mountPath: /var/cache/nginx
        - name: nginx-run
          mountPath: /var/run
      volumes:
      - name: tmp
        emptyDir: {}
      - name: nginx-cache
        emptyDir: {}
      - name: nginx-run
        emptyDir: {}
      securityContext:
        fsGroup: 1001
---
apiVersion: v1
kind: Service
metadata:
  name: fintranzo-web-service
  namespace: fintranzo
  labels:
    app: fintranzo-web
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
    name: http
  selector:
    app: fintranzo-web
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: fintranzo-web-ingress
  namespace: fintranzo
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - 19930913.xyz
    secretName: fintranzo-tls
  rules:
  - host: 19930913.xyz
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: fintranzo-web-service
            port:
              number: 80
