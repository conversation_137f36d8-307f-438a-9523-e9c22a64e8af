# 認證和通知系統測試指南

## 修復內容

### 1. 新增統一通知系統
- ✅ 創建了 `NotificationManager` 組件
- ✅ 支援 Toast 和 Modal 兩種通知類型
- ✅ 提供成功、錯誤、警告、信息四種通知樣式
- ✅ 自動動畫效果和自動隱藏功能

### 2. 更新認證流程
- ✅ 在 `authStore.ts` 中集成通知系統
- ✅ 登錄成功/失敗都會顯示相應通知
- ✅ Google 登錄成功/失敗都會顯示相應通知
- ✅ 註冊成功/失敗都會顯示相應通知
- ✅ 移除了重複的 Alert 調用

### 3. 錯誤處理改進
- ✅ 統一錯誤消息格式
- ✅ 針對不同錯誤類型提供友好提示
- ✅ Google 登錄取消、網路錯誤等特殊情況處理

## 測試步驟

### 1. 啟動應用
```bash
npm start
# 或
expo start
```

### 2. 測試通知系統（開發環境）
在瀏覽器控制台中執行：
```javascript
// 測試所有通知類型
testNotifications.all()

// 測試 Google 登錄場景
testNotifications.google()

// 測試註冊場景
testNotifications.registration()

// 測試連續通知
testNotifications.sequential()
```

### 3. 測試實際認證流程

#### 3.1 電子郵件登錄測試
1. 進入登錄頁面
2. 輸入錯誤的帳號密碼
   - ✅ 應該顯示錯誤通知（Modal）
3. 輸入正確的帳號密碼（<EMAIL> / user01）
   - ✅ 應該顯示成功通知（Toast）

#### 3.2 Google 登錄測試
1. 點擊 "體驗雲端同步" 或 Google 登錄按鈕
2. 如果取消登錄
   - ✅ 應該顯示取消通知
3. 如果登錄成功
   - ✅ 應該顯示成功通知（Toast）
4. 如果登錄失敗
   - ✅ 應該顯示錯誤通知（Modal）

#### 3.3 註冊測試
1. 進入註冊頁面
2. 輸入已存在的郵箱
   - ✅ 應該顯示錯誤通知（Modal）
3. 輸入新的郵箱和密碼
   - ✅ 應該顯示成功通知（Modal）

### 4. 檢查通知顯示效果

#### Toast 通知特徵：
- 從頂部滑入
- 自動消失（3-4秒）
- 可以點擊關閉
- 不阻塞用戶操作

#### Modal 通知特徵：
- 居中顯示
- 需要用戶點擊確定
- 半透明背景
- 阻塞用戶操作直到關閉

## 預期結果

### 登錄成功
- **通知類型**: Toast
- **標題**: "登錄成功"
- **內容**: "歡迎回來，{email}！"
- **圖標**: 綠色勾選

### 登錄失敗
- **通知類型**: Modal
- **標題**: "登錄失敗"
- **內容**: "帳號或密碼錯誤，請檢查後重試"
- **圖標**: 紅色錯誤

### Google 登錄成功
- **通知類型**: Toast
- **標題**: "Google 登錄成功"
- **內容**: "歡迎回來，{email}！"
- **圖標**: 綠色勾選

### Google 登錄失敗
- **通知類型**: Modal
- **標題**: "Google 登錄失敗"
- **內容**: 根據錯誤類型顯示相應消息
- **圖標**: 紅色錯誤

### 註冊成功
- **通知類型**: Modal（需要郵件確認）或 Toast（直接登錄）
- **標題**: "註冊成功"
- **內容**: 相應的成功消息
- **圖標**: 綠色勾選

### 註冊失敗
- **通知類型**: Modal
- **標題**: "註冊失敗"
- **內容**: 根據錯誤類型顯示相應消息
- **圖標**: 紅色錯誤

## 故障排除

### 如果通知不顯示
1. 檢查 `NotificationManager` 是否正確包裹在 App 組件中
2. 檢查控制台是否有錯誤
3. 確認 `notificationManager` 是否正確導入

### 如果通知樣式異常
1. 檢查 SafeAreaInsets 是否正常工作
2. 檢查 z-index 和 elevation 設置
3. 確認動畫是否正常執行

### 如果認證流程異常
1. 檢查 Supabase 配置
2. 檢查網路連接
3. 查看控制台日誌了解具體錯誤

## 技術細節

### 通知管理器架構
```
NotificationManager (Provider)
├── ToastNotification (多個)
└── ModalNotification (單個)
```

### 狀態管理
- 使用 React State 管理通知列表
- 全局 notificationManager 實例處理通知調度
- 支援多個 Toast 同時顯示，但 Modal 只能顯示一個

### 動畫效果
- Toast: 淡入淡出 + 滑動效果
- Modal: 淡入淡出效果
- 使用 React Native Animated API

這個修復確保了用戶在任何認證操作後都能收到清晰的反饋，提升了用戶體驗。
