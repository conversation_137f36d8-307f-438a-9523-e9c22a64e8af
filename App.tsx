// FinTranzo - 個人財務管理應用
import React from 'react';
import { StatusBar } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import TabNavigator from './src/navigation/TabNavigator';

export default function App() {
  console.log('🚀 FinTranzo 應用啟動');

  return (
    <>
      <StatusBar barStyle="light-content" backgroundColor="#2196F3" />
      <NavigationContainer>
        <TabNavigator />
      </NavigationContainer>
    </>
  );
}


