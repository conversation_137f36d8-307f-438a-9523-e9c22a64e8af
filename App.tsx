// 最小版本 React Native 應用 - 測試基本功能
import React from 'react';
import { View, Text, StyleSheet, StatusBar } from 'react-native';

export default function App() {
  console.log('🚀 最小版本 React Native 應用啟動');

  return (
    <>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      <View style={styles.container}>
        <Text style={styles.title}>FinTranzo 最小版本</Text>
        <Text style={styles.subtitle}>✅ 如果你看到這個畫面，表示應用成功運行</Text>
        <Text style={styles.info}>這是純 React Native 版本，沒有額外依賴</Text>
        <Text style={styles.success}>🎉 iOS 構建成功！</Text>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 10,
    textAlign: 'center',
    color: '#666',
  },
  info: {
    fontSize: 14,
    textAlign: 'center',
    color: '#999',
    marginBottom: 20,
  },
  success: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    color: '#4CAF50',
  },
});
