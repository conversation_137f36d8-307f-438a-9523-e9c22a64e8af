// 純 React Native 版本 - 測試 iOS 閃退問題
import React from 'react';
import { View, Text, StyleSheet, StatusBar } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';

export default function App() {
  console.log('🚀 純 React Native 版本啟動');

  return (
    <SafeAreaProvider>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      <View style={styles.container}>
        <Text style={styles.title}>FinTranzo 純原生版本</Text>
        <Text style={styles.subtitle}>如果你看到這個畫面，表示應用沒有閃退</Text>
        <Text style={styles.info}>這是純 React Native 版本，沒有 Expo</Text>
      </View>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 10,
    textAlign: 'center',
    color: '#666',
  },
  info: {
    fontSize: 14,
    textAlign: 'center',
    color: '#999',
  },
});
