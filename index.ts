// Minimal polyfills for React Native
import 'react-native-url-polyfill/auto';

// 暫時移除 Buffer polyfill 以修復 iOS 閃退問題
// import { Buffer } from 'buffer';
// global.Buffer = Buffer;

// Chrome 擴展錯誤修復（在 Web 環境中）
if (typeof window !== 'undefined') {
  import('./src/utils/chromeExtensionFix').then(({ chromeExtensionFix }) => {
    chromeExtensionFix.initialize();
  });
}

import {AppRegistry} from 'react-native';
import App from './App';
import {name as appName} from './app.json';

AppRegistry.registerComponent(appName, () => App);
