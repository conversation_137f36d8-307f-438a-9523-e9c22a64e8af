# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
.kotlin/
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# Environment Variables (CRITICAL SECURITY)
.env
.env.local
.env.development
.env.test
.env.production
.env.staging
.env*.local
*.env

# Supabase Keys and Secrets
supabase/.env
supabase/.env.local
.supabase/

# API Keys and Secrets
config/keys.js
config/secrets.js
secrets/
keys/

# Database Credentials
database.json
db-config.js

# typescript
*.tsbuildinfo

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
Thumbs.db
ehthumbs.db

# Temporary files
tmp/
temp/
.tmp/

# Build artifacts
build/
dist/
out/

# Test artifacts
test-results/
coverage/

# Backup files
*.backup
*.bak
*.old

# Documentation build
docs/build/

# Local development
.local/

# GitHub Actions artifacts
.github/workflows/*.log
