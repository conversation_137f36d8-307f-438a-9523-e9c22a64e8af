{"compilerOptions": {"target": "es2017", "lib": ["es2017", "es6", "dom"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "noImplicitAny": false, "strictNullChecks": false, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-native", "typeRoots": ["./types", "./node_modules/@types"]}, "include": ["**/*.ts", "**/*.tsx", "types/**/*.d.ts"]}