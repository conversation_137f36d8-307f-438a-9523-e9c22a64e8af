# Production Docker Compose for FinTranzo
# 完全模擬實際 Web + iOS 環境

version: '3.8'

services:
  # ===== Web 生產環境 =====
  fintranzo-web:
    build:
      context: .
      dockerfile: docker/Dockerfile.web
      target: production
    container_name: fintranzo-web-prod
    ports:
      - "80:80"
      - "443:443"
    environment:
      - NODE_ENV=production
      - EXPO_PUBLIC_PLATFORM=web
      - EXPO_PUBLIC_WEB_URL=https://19930913.xyz
    env_file:
      - .env.production
    volumes:
      - ./docker/ssl:/etc/ssl/certs:ro
    networks:
      - fintranzo-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.fintranzo-web.rule=Host(`19930913.xyz`)"
      - "traefik.http.routers.fintranzo-web.tls=true"
      - "traefik.http.routers.fintranzo-web.tls.certresolver=letsencrypt"

  # ===== iOS 模擬器環境 =====
  fintranzo-ios-simulator:
    build:
      context: .
      dockerfile: docker/Dockerfile.ios-simulator
    container_name: fintranzo-ios-simulator
    ports:
      - "19000:19000"  # Expo DevTools
      - "19001:19001"  # Expo Metro
      - "19002:19002"  # Expo Tunnel
      - "8081:8081"    # Metro Bundler
    environment:
      - NODE_ENV=development
      - EXPO_PUBLIC_PLATFORM=ios
      - EXPO_DEVTOOLS_LISTEN_ADDRESS=0.0.0.0
    env_file:
      - .env.production
    volumes:
      - expo-cache:/root/.expo
      - npm-cache:/root/.npm
      - ./src:/app/src:ro
    networks:
      - fintranzo-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:19000"]
      interval: 60s
      timeout: 15s
      retries: 3
      start_period: 120s

  # ===== 反向代理 =====
  traefik:
    image: traefik:v2.10
    container_name: fintranzo-traefik
    command:
      - "--api.dashboard=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      - "--certificatesresolvers.letsencrypt.acme.tlschallenge=true"
      - "--certificatesresolvers.letsencrypt.acme.email=<EMAIL>"
      - "--certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json"
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Traefik Dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik-letsencrypt:/letsencrypt
    networks:
      - fintranzo-network
    restart: unless-stopped

  # ===== 監控服務 =====
  prometheus:
    image: prom/prometheus:latest
    container_name: fintranzo-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - fintranzo-network
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: fintranzo-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - fintranzo-network
    restart: unless-stopped

  # ===== 日誌收集 =====
  loki:
    image: grafana/loki:latest
    container_name: fintranzo-loki
    ports:
      - "3100:3100"
    volumes:
      - ./monitoring/loki.yml:/etc/loki/local-config.yaml:ro
      - loki-data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - fintranzo-network
    restart: unless-stopped

  promtail:
    image: grafana/promtail:latest
    container_name: fintranzo-promtail
    volumes:
      - /var/log:/var/log:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - ./monitoring/promtail.yml:/etc/promtail/config.yml:ro
    command: -config.file=/etc/promtail/config.yml
    networks:
      - fintranzo-network
    restart: unless-stopped

  # ===== 測試服務 =====
  fintranzo-e2e-tests:
    build:
      context: .
      dockerfile: docker/Dockerfile.e2e-tests
    container_name: fintranzo-e2e-tests
    environment:
      - NODE_ENV=test
      - TEST_BASE_URL=http://fintranzo-web
      - EXPO_PUBLIC_PLATFORM=web
    env_file:
      - .env.production
    volumes:
      - ./test-results:/app/test-results
    networks:
      - fintranzo-network
    depends_on:
      - fintranzo-web
    profiles:
      - testing

  # ===== 數據庫備份 =====
  backup-service:
    image: alpine:latest
    container_name: fintranzo-backup
    environment:
      - SUPABASE_URL=${EXPO_PUBLIC_SUPABASE_URL}
      - SUPABASE_KEY=${EXPO_PUBLIC_SUPABASE_ANON_KEY}
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh:ro
    command: sh -c "while true; do sh /backup.sh; sleep 86400; done"
    networks:
      - fintranzo-network
    restart: unless-stopped
    profiles:
      - backup

volumes:
  expo-cache:
    driver: local
  npm-cache:
    driver: local
  traefik-letsencrypt:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  loki-data:
    driver: local

networks:
  fintranzo-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
