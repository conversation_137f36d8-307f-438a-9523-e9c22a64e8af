name: 📈 股票資料更新

on:
  schedule:
    # 台股更新時間：台灣時間 14:30 (UTC+8) - 台股收盤後
    - cron: '30 6 * * 1-5'
    # 美股更新時間：台灣時間 05:00 (UTC+8) - 美股收盤後
    - cron: '0 21 * * 1-5'
    # 匯率更新：每日台灣時間 09:00 (UTC+8)
    - cron: '0 1 * * 1-5'
  workflow_dispatch:
    inputs:
      update_type:
        description: '選擇要更新的資料類型'
        required: true
        default: 'all'
        type: choice
        options:
          - all
          - tw
          - tw-batch-1
          - tw-batch-2
          - tw-batch-3
          - tw-batch-4
          - tw-batch-5
          - us
          - rates

jobs:
  # 匯率更新
  update-rates:
    name: 💱 更新匯率
    runs-on: ubuntu-latest
    if: ${{ github.event_name == 'workflow_dispatch' && (github.event.inputs.update_type == 'all' || github.event.inputs.update_type == 'rates') || github.event_name == 'schedule' && github.event.schedule == '0 1 * * 1-5' }}

    steps:
    - name: 📥 檢出代碼
      uses: actions/checkout@v4

    - name: 🟢 設置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: 📦 安裝核心依賴
      run: |
        npm install --legacy-peer-deps @supabase/supabase-js@2.39.0 node-fetch@3.3.2 dotenv@16.3.1

    - name: 💱 執行匯率更新
      env:
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
      run: node scripts/update-exchange-rates.js

  # 台股更新 - 批次 1
  update-tw-batch-1:
    name: 🇹🇼 更新台股 批次 1/5 (最多700支)
    runs-on: ubuntu-latest
    if: ${{ github.event_name == 'workflow_dispatch' && (github.event.inputs.update_type == 'all' || github.event.inputs.update_type == 'tw' || github.event.inputs.update_type == 'tw-batch-1') || github.event_name == 'schedule' && github.event.schedule == '30 6 * * 1-5' }}

    steps:
    - name: 📥 檢出代碼
      uses: actions/checkout@v4

    - name: 🟢 設置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: 📦 安裝核心依賴
      run: |
        npm install --legacy-peer-deps @supabase/supabase-js@2.39.0 node-fetch@3.3.2 dotenv@16.3.1

    - name: 🏢 執行台股更新 - 批次 1
      env:
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
        BATCH_NUMBER: 1
        TOTAL_BATCHES: 5
      run: node scripts/update-taiwan-stocks.js

    - name: 📊 台股批次 1 更新結果
      if: always()
      run: |
        echo "台股批次 1/5 更新完成"

  # 台股更新 - 批次 2
  update-tw-batch-2:
    name: 🇹🇼 更新台股 批次 2/5 (最多700支)
    runs-on: ubuntu-latest
    needs: update-tw-batch-1
    if: ${{ always() && (github.event_name == 'workflow_dispatch' && (github.event.inputs.update_type == 'all' || github.event.inputs.update_type == 'tw' || github.event.inputs.update_type == 'tw-batch-2')) }}

    steps:
    - name: 📥 檢出代碼
      uses: actions/checkout@v4

    - name: 🟢 設置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: 📦 安裝核心依賴
      run: |
        npm install --legacy-peer-deps @supabase/supabase-js@2.39.0 node-fetch@3.3.2 dotenv@16.3.1

    - name: 🏢 執行台股更新 - 批次 2
      env:
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
        BATCH_NUMBER: 2
        TOTAL_BATCHES: 5
      run: node scripts/update-taiwan-stocks.js

    - name: 📊 台股批次 2 更新結果
      if: always()
      run: |
        echo "台股批次 2/5 更新完成"

  # 台股更新 - 批次 3
  update-tw-batch-3:
    name: 🇹🇼 更新台股 批次 3/5 (最多700支)
    runs-on: ubuntu-latest
    needs: update-tw-batch-2
    if: ${{ always() && (github.event_name == 'workflow_dispatch' && (github.event.inputs.update_type == 'all' || github.event.inputs.update_type == 'tw' || github.event.inputs.update_type == 'tw-batch-3')) }}

    steps:
    - name: 📥 檢出代碼
      uses: actions/checkout@v4

    - name: 🟢 設置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: 📦 安裝核心依賴
      run: |
        npm install --legacy-peer-deps @supabase/supabase-js@2.39.0 node-fetch@3.3.2 dotenv@16.3.1

    - name: 🏢 執行台股更新 - 批次 3
      env:
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
        BATCH_NUMBER: 3
        TOTAL_BATCHES: 5
      run: node scripts/update-taiwan-stocks.js

    - name: 📊 台股批次 3 更新結果
      if: always()
      run: |
        echo "台股批次 3/5 更新完成"

  # 台股更新 - 批次 4
  update-tw-batch-4:
    name: 🇹🇼 更新台股 批次 4/5 (最多700支)
    runs-on: ubuntu-latest
    needs: update-tw-batch-3
    if: ${{ always() && (github.event_name == 'workflow_dispatch' && (github.event.inputs.update_type == 'all' || github.event.inputs.update_type == 'tw' || github.event.inputs.update_type == 'tw-batch-4')) }}

    steps:
    - name: 📥 檢出代碼
      uses: actions/checkout@v4

    - name: 🟢 設置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: 📦 安裝核心依賴
      run: |
        npm install --legacy-peer-deps @supabase/supabase-js@2.39.0 node-fetch@3.3.2 dotenv@16.3.1

    - name: 🏢 執行台股更新 - 批次 4
      env:
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
        BATCH_NUMBER: 4
        TOTAL_BATCHES: 5
      run: node scripts/update-taiwan-stocks.js

    - name: 📊 台股批次 4 更新結果
      if: always()
      run: |
        echo "台股批次 4/5 更新完成"

  # 台股更新 - 批次 5
  update-tw-batch-5:
    name: 🇹🇼 更新台股 批次 5/5 (最多700支)
    runs-on: ubuntu-latest
    needs: update-tw-batch-4
    if: ${{ always() && (github.event_name == 'workflow_dispatch' && (github.event.inputs.update_type == 'all' || github.event.inputs.update_type == 'tw' || github.event.inputs.update_type == 'tw-batch-5')) }}

    steps:
    - name: 📥 檢出代碼
      uses: actions/checkout@v4

    - name: 🟢 設置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: 📦 安裝核心依賴
      run: |
        npm install --legacy-peer-deps @supabase/supabase-js@2.39.0 node-fetch@3.3.2 dotenv@16.3.1

    - name: 🏢 執行台股更新 - 批次 5
      env:
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
        BATCH_NUMBER: 5
        TOTAL_BATCHES: 5
      run: node scripts/update-taiwan-stocks.js

    - name: 📊 台股批次 5 更新結果
      if: always()
      run: |
        echo "台股批次 5/5 更新完成 - 所有台股更新完成！"

  # 美股更新
  update-us:
    name: 🇺🇸 更新美股 (Yahoo Finance)
    runs-on: ubuntu-latest
    needs: update-tw-batch-5
    if: ${{ always() && (github.event_name == 'workflow_dispatch' && (github.event.inputs.update_type == 'all' || github.event.inputs.update_type == 'us') || github.event_name == 'schedule' && github.event.schedule == '0 21 * * 1-5') }}

    steps:
    - name: 📥 檢出代碼
      uses: actions/checkout@v4

    - name: 🟢 設置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: 📦 安裝核心依賴
      run: |
        npm install --legacy-peer-deps @supabase/supabase-js@2.39.0 node-fetch@3.3.2 dotenv@16.3.1 csv-parse@5.6.0

    - name: 🏢 執行美股更新
      env:
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
      run: node scripts/update-us-stocks.js

    - name: 📊 美股更新結果
      if: always()
      run: |
        echo "美股更新完成 - 使用 Yahoo Finance API"
