# FinTranzo 環境變數範本
# 複製此檔案為 .env 並填入實際值

# Supabase 配置
EXPO_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# API 配置
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key_here
YAHOO_FINANCE_API_KEY=your_yahoo_finance_api_key_here

# 開發環境配置
NODE_ENV=development
DEBUG=false

# 應用程式配置
APP_NAME=FinTranzo
APP_VERSION=1.0.0

# 資料庫配置
DATABASE_URL=your_database_url_here

# 第三方服務
SENTRY_DSN=your_sentry_dsn_here
ANALYTICS_ID=your_analytics_id_here

# 安全配置
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here

# 通知配置
PUSH_NOTIFICATION_KEY=your_push_notification_key_here
EMAIL_SERVICE_KEY=your_email_service_key_here

# 說明：
# 1. 複製此檔案為 .env
# 2. 填入實際的 API 金鑰和配置值
# 3. 永遠不要提交 .env 檔案到 Git
# 4. 在 Supabase Dashboard 中獲取您的金鑰：
#    - 前往 Settings > API
#    - 複製 Project URL 和 anon public key
#    - 複製 service_role secret key (僅用於伺服器端)
