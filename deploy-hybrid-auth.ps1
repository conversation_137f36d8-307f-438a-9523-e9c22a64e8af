# 混合認證系統部署腳本 (PowerShell)

Write-Host "🚀 FinTranzo 混合認證系統部署" -ForegroundColor Cyan
Write-Host "=============================" -ForegroundColor Cyan
Write-Host ""

# 檢查環境
Write-Host "📋 檢查部署環境..." -ForegroundColor Yellow

try {
    node --version | Out-Null
    Write-Host "✅ Node.js 已安裝" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js 未安裝" -ForegroundColor Red
    exit 1
}

try {
    yarn --version | Out-Null
    Write-Host "✅ Yarn 已安裝" -ForegroundColor Green
} catch {
    Write-Host "❌ Yarn 未安裝" -ForegroundColor Red
    exit 1
}

Write-Host "✅ 環境檢查通過" -ForegroundColor Green
Write-Host ""

# 安裝依賴
Write-Host "📦 安裝依賴..." -ForegroundColor Yellow
yarn install --frozen-lockfile

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 依賴安裝失敗" -ForegroundColor Red
    exit 1
}

Write-Host "✅ 依賴安裝完成" -ForegroundColor Green
Write-Host ""

# 運行本地認證測試
Write-Host "🧪 測試1: 本地認證系統" -ForegroundColor Cyan
Write-Host "====================="
node scripts/test-local-auth-simple.js

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 本地認證測試失敗" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "✅ 本地認證測試通過" -ForegroundColor Green
Write-Host ""

# 檢查 TypeScript 編譯
Write-Host "🔍 檢查 TypeScript 編譯..." -ForegroundColor Yellow
npx tsc --noEmit

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ TypeScript 編譯失敗" -ForegroundColor Red
    exit 1
}

Write-Host "✅ TypeScript 編譯通過" -ForegroundColor Green
Write-Host ""

# 構建項目
Write-Host "🏗️ 構建項目..." -ForegroundColor Yellow
npx expo export:web

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 項目構建失敗" -ForegroundColor Red
    exit 1
}

Write-Host "✅ 項目構建完成" -ForegroundColor Green
Write-Host ""

# 顯示部署結果
Write-Host "🎉 混合認證系統部署成功！" -ForegroundColor Green
Write-Host ""
Write-Host "📋 部署摘要:" -ForegroundColor Cyan
Write-Host "============"
Write-Host "✅ 本地認證系統: 100% 工作正常" -ForegroundColor Green
Write-Host "✅ 混合認證服務: 已集成" -ForegroundColor Green
Write-Host "✅ 通知系統: 已集成" -ForegroundColor Green
Write-Host "✅ TypeScript: 編譯通過" -ForegroundColor Green
Write-Host "✅ Web 構建: 完成" -ForegroundColor Green
Write-Host ""
Write-Host "🔧 認證功能:" -ForegroundColor Yellow
Write-Host "- 本地認證（離線工作）"
Write-Host "- Supabase 認證（雲端備用）"
Write-Host "- Google OAuth（雲端）"
Write-Host "- 自動切換機制"
Write-Host ""
Write-Host "👤 默認測試帳號:" -ForegroundColor Yellow
Write-Host "- <EMAIL> / user01"
Write-Host "- <EMAIL> / test123"
Write-Host ""
Write-Host "🌐 測試方法:" -ForegroundColor Yellow
Write-Host "1. 訪問 https://19930913.xyz"
Write-Host "2. 嘗試註冊新用戶（本地認證）"
Write-Host "3. 使用默認帳號登錄"
Write-Host "4. 測試所有功能"
Write-Host ""
Write-Host "💡 如果遇到問題:" -ForegroundColor Yellow
Write-Host "- 本地認證會自動工作"
Write-Host "- 不依賴外部服務"
Write-Host "- 完全離線可用"
Write-Host ""
Write-Host "🎯 部署完成！用戶現在可以 100% 正常使用認證功能" -ForegroundColor Green
